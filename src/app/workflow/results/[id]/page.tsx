/**
 * Workflow Results Page
 * Display the generated content from a completed workflow
 */

'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';

interface WorkflowResults {
  execution: {
    id: string;
    workflowId: string;
    status: string;
    progress: number;
    startedAt: string;
    completedAt?: string;
    inputs: Record<string, any>;
    currentStep?: string;
  };
  workflow: {
    id: string;
    name: string;
    description: string;
  } | null;
  steps: Array<{
    stepId: string;
    status: string;
    startedAt: string;
    completedAt?: string;
    duration?: number;
    inputs: Record<string, any>;
    outputs: Record<string, any>;
    error?: string;
    stepType?: string;
    approvalRequired?: boolean;
    artifactId?: string;
  }>;
  content: Array<{
    id: string;
    type: string;
    title: string;
    content: any;
    status: string;
    stepId: string;
    createdAt: string;
    metadata?: Record<string, any>;
  }>;
  artifacts: Array<{
    id: string;
    type: string;
    title: string;
    content: any;
    status: string;
    stepId: string;
    executionId: string;
    createdAt: string;
    approvedBy?: string;
    approvedAt?: string;
  }>;
}

export default function WorkflowResultsPage() {
  const params = useParams();
  const router = useRouter();
  const executionId = params.id as string;

  const [results, setResults] = useState<WorkflowResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [publishingStatus, setPublishingStatus] = useState<Record<string, 'idle' | 'publishing' | 'published' | 'failed'>>({});
  const [publishResults, setPublishResults] = useState<Record<string, { url?: string; error?: string }>>({});

  useEffect(() => {
    loadResults();
  }, [executionId]);

  const loadResults = async () => {
    try {
      // Use the correct Workflow Results API
      const response = await fetch(`/api/workflow/results/${executionId}`);
      const result = await response.json();

      if (result.success && result.data) {
        // Debug: Log the received data
        console.log('🔍 Results API Response:', result.data);

        // The data is already in the correct format from the results API
        const execution = result.data.execution;
        const workflow = result.data.workflow;
        const artifacts = result.data.artifacts || [];
        const steps = result.data.steps || [];

        console.log('📊 Parsed data:', {
          execution: execution?.id,
          workflow: workflow?.name,
          artifactsCount: artifacts.length,
          stepsCount: steps.length,
          artifacts: artifacts.map((a: any) => ({ id: a.id, title: a.title, status: a.status }))
        });

        const transformedResults: WorkflowResults = {
          execution: {
            id: execution.id,
            workflowId: execution.workflowId,
            status: execution.status,
            progress: execution.progress,
            startedAt: execution.startedAt,
            completedAt: execution.completedAt,
            inputs: execution.inputs,
            currentStep: execution.currentStep
          },
          workflow: workflow ? {
            id: workflow.id,
            name: workflow.name,
            description: workflow.description
          } : null,
          steps: steps.length > 0 ? steps : Object.values(execution.stepResults || {}).map((step: any) => ({
            stepId: step.stepId,
            status: step.status,
            startedAt: step.startedAt,
            completedAt: step.completedAt,
            duration: step.duration,
            inputs: step.inputs || {},
            outputs: step.outputs || {},
            error: step.error,
            stepType: step.stepType,
            approvalRequired: step.approvalRequired,
            artifactId: step.artifactId
          })),
          content: [], // Legacy content - will be replaced by artifacts
          artifacts: artifacts
        };

        setResults(transformedResults);
      } else {
        setError(result.error || 'Failed to load results');
      }
    } catch (err) {
      setError('Failed to load results');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const formatContent = (content: any): string => {
    if (typeof content === 'string') {
      return content;
    }
    return JSON.stringify(content, null, 2);
  };

  const formatDuration = (duration?: number): string => {
    if (!duration) return 'N/A';
    return `${(duration / 1000).toFixed(1)}s`;
  };

  const publishToCMS = async (artifactId: string, artifact: any) => {
    setPublishingStatus(prev => ({ ...prev, [artifactId]: 'publishing' }));

    try {
      // Publish to CMS (Payload CMS integration)
      const response = await fetch(`/api/cms/publish`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          artifactId,
          title: artifact.title,
          content: artifact.content,
          type: artifact.type,
          executionId: artifact.executionId,
          stepId: artifact.stepId,
          metadata: {
            workflowGenerated: true,
            originalArtifactId: artifactId,
            generatedAt: artifact.createdAt,
            approvedBy: artifact.approvedBy,
            approvedAt: artifact.approvedAt
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        setPublishingStatus(prev => ({ ...prev, [artifactId]: 'published' }));
        setPublishResults(prev => ({
          ...prev,
          [artifactId]: { url: result.data?.url || result.data?.id }
        }));
        setError('');
      } else {
        setPublishingStatus(prev => ({ ...prev, [artifactId]: 'failed' }));
        setPublishResults(prev => ({
          ...prev,
          [artifactId]: { error: result.error || 'Failed to publish' }
        }));
      }
    } catch (err) {
      setPublishingStatus(prev => ({ ...prev, [artifactId]: 'failed' }));
      setPublishResults(prev => ({
        ...prev,
        [artifactId]: { error: 'Failed to publish to CMS' }
      }));
      console.error('CMS publish error:', err);
    }
  };

  const downloadArtifact = (artifact: any) => {
    const content = typeof artifact.content === 'string'
      ? artifact.content
      : JSON.stringify(artifact.content, null, 2);

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${artifact.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const isPublishable = (artifact: any): boolean => {
    return artifact.status === 'approved';
  };

  const getPublishStatus = (artifactId: string): string => {
    const status = publishingStatus[artifactId] || 'idle';
    switch (status) {
      case 'publishing': return 'Publishing...';
      case 'published': return 'Published';
      case 'failed': return 'Failed';
      default: return 'Ready to Publish';
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-700';
      case 'rejected': return 'bg-red-100 text-red-700';
      case 'pending_approval': return 'bg-yellow-100 text-yellow-700';
      case 'draft': return 'bg-gray-100 text-gray-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="text-center py-12">
          <p className="text-gray-600">No results found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">Workflow Results</h1>
            <p className="text-gray-600 mb-3">View approved artifacts and publish them to your CMS</p>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>Execution ID: {results.execution.id}</span>
              <span>Status: <span className={`font-medium ${
                results.execution.status === 'completed' ? 'text-green-600' :
                results.execution.status === 'failed' ? 'text-red-600' :
                'text-blue-600'
              }`}>{results.execution.status}</span></span>
              <span>Progress: {results.execution.progress}%</span>
            </div>
          </div>

          <div className="flex gap-3">
            <button
              onClick={() => router.push('/workflow')}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              ← Back to Workflow
            </button>
            <button
              onClick={() => router.push('/dashboard')}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Dashboard
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              🔄 Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Workflow Info */}
      {results.workflow && (
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="font-medium mb-2">Workflow: {results.workflow.name}</h3>
          <p className="text-sm text-gray-600">{results.workflow.description}</p>
        </div>
      )}

      {/* Execution Summary */}
      <div className="bg-white border rounded-lg p-4 mb-6">
        <h3 className="font-medium mb-3">Execution Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Started:</span>
            <p>{new Date(results.execution.startedAt).toLocaleString()}</p>
          </div>
          {results.execution.completedAt && (
            <div>
              <span className="text-gray-500">Completed:</span>
              <p>{new Date(results.execution.completedAt).toLocaleString()}</p>
            </div>
          )}
          <div>
            <span className="text-gray-500">Total Steps:</span>
            <p>{results.steps.length}</p>
          </div>
          <div>
            <span className="text-gray-500">Artifacts:</span>
            <p>{results.artifacts.length}</p>
          </div>
        </div>
      </div>

      {/* Publishing Status Summary */}
      {results.artifacts.length > 0 && (
        <div className="bg-white border rounded-lg p-4 mb-6">
          <h3 className="font-medium mb-3">Publishing Status</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Ready to Publish:</span>
              <p className="text-blue-600 font-medium">
                {results.artifacts.filter(a => isPublishable(a) && !publishingStatus[a.id]).length}
              </p>
            </div>
            <div>
              <span className="text-gray-500">Published:</span>
              <p className="text-green-600 font-medium">
                {Object.values(publishingStatus).filter(status => status === 'published').length}
              </p>
            </div>
            <div>
              <span className="text-gray-500">Failed:</span>
              <p className="text-red-600 font-medium">
                {Object.values(publishingStatus).filter(status => status === 'failed').length}
              </p>
            </div>
            <div>
              <span className="text-gray-500">Total Artifacts:</span>
              <p className="font-medium">
                {results.artifacts.length}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Generated Artifacts */}
      {results.artifacts.length > 0 && (
        <div className="mb-6">
          <h3 className="text-xl font-semibold mb-4">Generated Artifacts</h3>
          <div className="space-y-4">
            {results.artifacts.map(artifact => (
              <div key={artifact.id} className="bg-white border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium">{artifact.title}</h4>
                  <div className="flex gap-2 text-xs">
                    <span className="bg-gray-100 px-2 py-1 rounded">{artifact.type}</span>
                    <span className={`px-2 py-1 rounded ${getStatusColor(artifact.status)}`}>
                      {artifact.status}
                    </span>
                    {artifact.approvedBy && (
                      <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded">
                        Approved by {artifact.approvedBy}
                      </span>
                    )}
                  </div>
                </div>

                <div className="bg-gray-50 p-3 rounded border">
                  <pre className="whitespace-pre-wrap text-sm">
                    {formatContent(artifact.content)}
                  </pre>
                </div>

                {/* CMS Publishing Section */}
                {isPublishable(artifact) && (
                  <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">
                    <h5 className="font-medium text-blue-800 mb-3">Ready for Publishing</h5>

                    {/* Publishing Status */}
                    <div className="mb-3">
                      <span className="text-sm text-gray-600">Status: </span>
                      <span className={`text-sm font-medium ${
                        publishingStatus[artifact.id] === 'published' ? 'text-green-600' :
                        publishingStatus[artifact.id] === 'failed' ? 'text-red-600' :
                        publishingStatus[artifact.id] === 'publishing' ? 'text-blue-600' :
                        'text-gray-600'
                      }`}>
                        {getPublishStatus(artifact.id)}
                      </span>
                    </div>

                    {/* Published URL */}
                    {publishResults[artifact.id]?.url && (
                      <div className="mb-3 p-2 bg-green-50 border border-green-200 rounded">
                        <span className="text-sm text-green-700">Published at: </span>
                        <a
                          href={publishResults[artifact.id].url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:underline"
                        >
                          {publishResults[artifact.id].url}
                        </a>
                      </div>
                    )}

                    {/* Error Message */}
                    {publishResults[artifact.id]?.error && (
                      <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded">
                        <span className="text-sm text-red-700">Error: {publishResults[artifact.id].error}</span>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <button
                        onClick={() => publishToCMS(artifact.id, artifact)}
                        disabled={publishingStatus[artifact.id] === 'publishing'}
                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {publishingStatus[artifact.id] === 'publishing' ? 'Publishing...' : '📤 Publish to CMS'}
                      </button>
                      <button
                        onClick={() => downloadArtifact(artifact)}
                        className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                      >
                        📥 Download
                      </button>
                    </div>
                  </div>
                )}

                {/* Not Approved Message */}
                {!isPublishable(artifact) && (
                  <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded">
                    <p className="text-sm text-gray-600">
                      {artifact.status === 'pending_approval' ?
                        '⏳ Waiting for approval before publishing' :
                        `❌ Cannot publish - Status: ${artifact.status}`
                      }
                    </p>
                  </div>
                )}

                {/* Artifact Metadata */}
                <div className="mt-3 text-xs text-gray-500">
                  <div className="flex gap-4">
                    <span>Created: {new Date(artifact.createdAt).toLocaleString()}</span>
                    {artifact.approvedAt && (
                      <span>Approved: {new Date(artifact.approvedAt).toLocaleString()}</span>
                    )}
                    <span>Type: {artifact.type}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Step Details */}
      <div>
        <h3 className="text-xl font-semibold mb-4">Step Details</h3>
        <div className="space-y-3">
          {results.steps.map(step => (
            <div key={step.stepId} className="bg-white border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">{step.stepId}</h4>
                <div className="flex gap-2 text-xs">
                  <span className={`px-2 py-1 rounded ${
                    step.status === 'completed' ? 'bg-green-100 text-green-700' :
                    step.status === 'failed' ? 'bg-red-100 text-red-700' :
                    step.status === 'running' ? 'bg-blue-100 text-blue-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {step.status}
                  </span>
                  <span className="bg-gray-100 px-2 py-1 rounded">
                    {formatDuration(step.duration)}
                  </span>
                </div>
              </div>

              {step.error && (
                <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
                  Error: {step.error}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500 font-medium">Inputs:</span>
                  <pre className="mt-1 text-xs bg-gray-50 p-2 rounded overflow-auto">
                    {JSON.stringify(step.inputs, null, 2)}
                  </pre>
                </div>
                <div>
                  <span className="text-gray-500 font-medium">Outputs:</span>
                  <pre className="mt-1 text-xs bg-gray-50 p-2 rounded overflow-auto">
                    {JSON.stringify(step.outputs, null, 2)}
                  </pre>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
