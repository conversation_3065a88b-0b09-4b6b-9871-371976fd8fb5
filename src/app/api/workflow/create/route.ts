/**
 * Workflow Creation API
 * Simple API to create and execute workflows
 */

import { NextRequest, NextResponse } from 'next/server';
import {
  getWorkflowEngine,
  getTemplateRegistry
} from '../../../../core/workflow/singleton';
import { ResponseFormatter } from '../../../../core/api/response-formatter';
import { ErrorType } from '../../../../core/utils/error-handler';

export async function POST(request: NextRequest) {
  const requestId = ResponseFormatter.getRequestId(request);

  try {
    const body = await request.json();
    const { templateId, inputs, userApiKey } = body;

    // Validate required fields
    if (!templateId) {
      return ResponseFormatter.validationError(
        'Template ID is required',
        [{ field: 'templateId', message: 'Template ID is required', code: 'REQUIRED' }],
        requestId
      );
    }

    if (!inputs) {
      return ResponseFormatter.validationError(
        'Inputs are required',
        [{ field: 'inputs', message: 'Inputs are required', code: 'REQUIRED' }],
        requestId
      );
    }

    // Get singletons
    const templateRegistry = getTemplateRegistry();
    const workflowEngine = getWorkflowEngine();

    // Get template
    const template = templateRegistry.getTemplate(templateId);
    if (!template) {
      return ResponseFormatter.notFound('Template', templateId, requestId);
    }

    // Create workflow from template
    const workflowId = await workflowEngine.createWorkflow(template.workflow);

    // Prepare inputs with user API key if provided
    const executionInputs = {
      ...inputs,
      userApiKey // Pass user's API key for BYOK
    };

    // Execute workflow
    const executionId = await workflowEngine.executeWorkflow(
      workflowId,
      executionInputs,
      {
        source: 'api',
        priority: 'normal'
      }
    );

    return ResponseFormatter.success({
      workflowId,
      executionId,
      templateId,
      status: 'started'
    }, requestId);

  } catch (error) {
    console.error('Workflow creation error:', error);
    return ResponseFormatter.fromError(error, requestId);
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const executionId = searchParams.get('executionId');

    // Get singletons
    const templateRegistry = getTemplateRegistry();
    const workflowEngine = getWorkflowEngine();

    if (!executionId) {
      // Return available templates
      const templates = templateRegistry.getAllTemplates();

      return NextResponse.json({
        success: true,
        data: {
          templates: templates.map(template => ({
            id: template.id,
            name: template.name,
            description: template.description,
            instructions: template.instructions || template.description || '',
            featured: template.featured,
            sampleInputs: template.sampleInputs,
            workflow: {
              metadata: {
                category: template.workflow.metadata.category,
                difficulty: template.workflow.metadata.difficulty,
                estimatedTime: template.workflow.metadata.estimatedTime
              }
            }
          }))
        }
      });
    }

    // Get execution status
    const execution = await workflowEngine.getExecution(executionId);
    if (!execution) {
      return NextResponse.json(
        { error: 'Execution not found' },
        { status: 404 }
      );
    }

    // Get workflow details
    const workflow = await workflowEngine.getWorkflow(execution.workflowId);

    return NextResponse.json({
      success: true,
      data: {
        execution: {
          id: execution.id,
          workflowId: execution.workflowId,
          status: execution.status,
          progress: execution.progress,
          currentStep: execution.currentStep,
          startedAt: execution.startedAt,
          completedAt: execution.completedAt,
          error: execution.error
        },
        workflow: workflow ? {
          id: workflow.id,
          name: workflow.name,
          description: workflow.description
        } : null,
        steps: Object.values(execution.stepResults).map(step => ({
          stepId: step.stepId,
          status: step.status,
          startedAt: step.startedAt,
          completedAt: step.completedAt,
          duration: step.duration,
          error: step.error,
          artifactId: step.artifactId,
          approvalRequired: step.approvalRequired,
          approvedBy: step.approvedBy,
          approvedAt: step.approvedAt,
          rejectionReason: step.rejectionReason,
          metadata: step.metadata
        }))
      }
    });

  } catch (error) {
    console.error('Workflow status error:', error);

    return NextResponse.json(
      {
        error: 'Failed to get workflow status',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
