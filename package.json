{"name": "authenciocms", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@langchain/community": "^0.3.41", "@langchain/core": "^0.3.46", "@langchain/langgraph": "^0.2.65", "@langchain/openai": "^0.5.6", "@mui/icons-material": "^7.0.2", "@mui/lab": "^7.0.0-beta.11", "@mui/material": "^7.0.2", "@payloadcms/db-postgres": "latest", "@payloadcms/next": "latest", "@payloadcms/payload-cloud": "latest", "@payloadcms/richtext-lexical": "latest", "@payloadcms/storage-gcs": "^3.36.1", "@tailwindcss/postcss": "^4.1.8", "@upstash/redis": "^1.34.9", "cross-env": "^7.0.3", "csv-parse": "^5.6.0", "csv-stringify": "^6.5.2", "graphql": "^16.8.1", "next": "15.0.4", "openai": "^4.89.0", "payload": "latest", "react": "19.0.0", "react-dom": "19.0.0", "react-force-graph-2d": "^1.27.1", "react-markdown": "^10.1.0", "react18-json-view": "^0.2.9", "remark-gfm": "^4.0.1", "sharp": "0.32.6", "uuid": "^11.1.0"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.5.4", "@types/react": "19.0.1", "@types/react-dom": "19.0.1", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "15.0.4", "jest": "^29.7.0", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "ts-jest": "^29.3.4", "typescript": "5.7.2"}, "engines": {"node": "^18.20.2 || >=20.9.0"}}